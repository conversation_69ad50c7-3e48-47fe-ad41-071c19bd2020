import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { hashPassword, comparePassword } from '@/utils/crypto.util'
import { ErrorFactory } from '@/utils/errors.util'

export interface CreateUserInput {
  username: string
  email: string
  password: string
  fullName: string
  roleId: string
  department?: string
  phone?: string
  isActive?: boolean
}

export interface UpdateUserInput {
  username?: string
  email?: string
  fullName?: string
  roleId?: string
  department?: string
  phone?: string
  isActive?: boolean
}

export interface ChangePasswordInput {
  currentPassword: string
  newPassword: string
}

export interface UserWithRole {
  id: string
  username: string
  email: string
  fullName: string
  department: string | null
  phone: string | null
  isActive: boolean
  lastLoginAt: Date | null
  createdAt: Date
  updatedAt: Date
  role: {
    id: string
    name: string
    description: string
    permissions: string[]
  }
}

export class UserService {
  /**
   * 创建用户
   */
  static async createUser(data: CreateUserInput): Promise<UserWithRole> {
    // 检查用户名唯一性
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: data.username },
          { email: data.email }
        ]
      }
    })

    if (existingUser) {
      if (existingUser.username === data.username) {
        throw ErrorFactory.conflict('用户名已存在')
      }
      if (existingUser.email === data.email) {
        throw ErrorFactory.conflict('邮箱已存在')
      }
    }

    // 检查角色是否存在
    const role = await prisma.role.findUnique({
      where: { id: data.roleId }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    // 加密密码
    const hashedPassword = await hashPassword(data.password)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username: data.username,
        email: data.email,
        password: hashedPassword,
        fullName: data.fullName,
        roleId: data.roleId,
        department: data.department || null,
        phone: data.phone || null,
        isActive: data.isActive ?? true
      },
      include: {
        role: true
      }
    })

    return this.formatUserWithRole(user)
  }

  /**
   * 获取用户列表
   */
  static async getUsers(
    page: number = 1,
    limit: number = 20,
    search?: string,
    roleId?: string,
    isActive?: boolean,
    department?: string
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { username: { contains: search } },
        { fullName: { contains: search } },
        { email: { contains: search } }
      ]
    }
    
    if (roleId) {
      where.roleId = roleId
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive
    }
    
    if (department) {
      where.department = department
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        include: {
          role: true
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ])

    return {
      users: users.map(user => this.formatUserWithRole(user)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取用户详情
   */
  static async getUserById(id: string): Promise<UserWithRole> {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        role: true
      }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    return this.formatUserWithRole(user)
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: string, data: UpdateUserInput): Promise<UserWithRole> {
    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      throw ErrorFactory.notFound('用户')
    }

    // 检查用户名和邮箱唯一性
    if (data.username || data.email) {
      const conflictUser = await prisma.user.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                ...(data.username ? [{ username: data.username }] : []),
                ...(data.email ? [{ email: data.email }] : [])
              ]
            }
          ]
        }
      })

      if (conflictUser) {
        if (conflictUser.username === data.username) {
          throw ErrorFactory.conflict('用户名已存在')
        }
        if (conflictUser.email === data.email) {
          throw ErrorFactory.conflict('邮箱已存在')
        }
      }
    }

    // 检查角色是否存在
    if (data.roleId) {
      const role = await prisma.role.findUnique({
        where: { id: data.roleId }
      })

      if (!role) {
        throw ErrorFactory.notFound('角色')
      }
    }

    // 更新用户
    const user = await prisma.user.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      },
      include: {
        role: true
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${id}`)

    return this.formatUserWithRole(user)
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: string): Promise<void> {
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    // 检查是否有关联数据
    const [serviceCount, workLogCount, commentCount] = await Promise.all([
      prisma.service.count({ where: { assignedTo: id } }),
      prisma.serviceWorkLog.count({ where: { userId: id } }),
      prisma.serviceComment.count({ where: { authorId: id } })
    ])

    if (serviceCount > 0 || workLogCount > 0 || commentCount > 0) {
      throw ErrorFactory.business('用户有关联数据，无法删除。请先转移或删除相关数据。')
    }

    // 删除用户
    await prisma.user.delete({
      where: { id }
    })

    // 清除用户缓存
    await CacheService.del(`user:${id}`)
  }

  /**
   * 修改密码
   */
  static async changePassword(
    userId: string,
    data: ChangePasswordInput
  ): Promise<void> {
    // 获取用户当前密码
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { password: true }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    // 验证当前密码
    const isCurrentPasswordValid = await comparePassword(
      data.currentPassword,
      user.password
    )

    if (!isCurrentPasswordValid) {
      throw ErrorFactory.business('当前密码不正确')
    }

    // 加密新密码
    const hashedNewPassword = await hashPassword(data.newPassword)

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date()
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${userId}`)
  }

  /**
   * 重置用户密码（管理员功能）
   */
  static async resetPassword(
    userId: string,
    newPassword: string
  ): Promise<void> {
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    // 加密新密码
    const hashedPassword = await hashPassword(newPassword)

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        updatedAt: new Date()
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${userId}`)
  }

  /**
   * 切换用户状态
   */
  static async toggleUserStatus(id: string): Promise<UserWithRole> {
    const user = await prisma.user.findUnique({
      where: { id }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        isActive: !user.isActive,
        updatedAt: new Date()
      },
      include: {
        role: true
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${id}`)

    return this.formatUserWithRole(updatedUser)
  }

  /**
   * 更新用户最后登录时间
   */
  static async updateLastLogin(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: {
        lastLoginAt: new Date()
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${userId}`)
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats() {
    const [
      totalUsers,
      activeUsers,
      recentUsers,
      usersByRole,
      usersByDepartment
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
          }
        }
      }),
      prisma.user.groupBy({
        by: ['roleId'],
        _count: { id: true },
        include: {
          role: {
            select: { name: true }
          }
        }
      }),
      prisma.user.groupBy({
        by: ['department'],
        _count: { id: true },
        where: {
          department: { not: null }
        }
      })
    ])

    return {
      totalUsers,
      activeUsers,
      inactiveUsers: totalUsers - activeUsers,
      recentUsers,
      usersByRole: usersByRole.map(item => ({
        roleId: item.roleId,
        count: item._count.id
      })),
      usersByDepartment: usersByDepartment.map(item => ({
        department: item.department,
        count: item._count.id
      }))
    }
  }

  /**
   * 获取部门列表
   */
  static async getDepartments(): Promise<string[]> {
    const departments = await prisma.user.findMany({
      where: {
        department: { not: null }
      },
      select: {
        department: true
      },
      distinct: ['department']
    })

    return departments
      .map(item => item.department)
      .filter(Boolean) as string[]
  }

  /**
   * 格式化用户数据
   */
  private static formatUserWithRole(user: any): UserWithRole {
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      department: user.department,
      phone: user.phone,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      role: {
        id: user.role.id,
        name: user.role.name,
        description: user.role.description,
        permissions: typeof user.role.permissions === 'string' 
          ? JSON.parse(user.role.permissions) 
          : user.role.permissions
      }
    }
  }

  /**
   * 批量导入用户
   */
  static async importUsers(users: CreateUserInput[]): Promise<{
    success: UserWithRole[]
    failed: { user: CreateUserInput, error: string }[]
  }> {
    const success: UserWithRole[] = []
    const failed: { user: CreateUserInput, error: string }[] = []

    for (const userData of users) {
      try {
        const user = await this.createUser(userData)
        success.push(user)
      } catch (error) {
        failed.push({
          user: userData,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    return { success, failed }
  }
}