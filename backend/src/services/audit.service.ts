import { prisma } from '@/config/database.config'

export interface AuditLogEntry {
  userId: string
  action: string
  resource: string
  resourceId?: string | null
  details?: Record<string, any> | null
  ipAddress?: string | null
  userAgent?: string | null
}

export class AuditService {
  /**
   * 记录操作日志
   */
  static async log(entry: AuditLogEntry): Promise<void> {
    try {
      await prisma.operationLog.create({
        data: {
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource,
          resourceId: entry.resourceId,
          details: entry.details ? JSON.stringify(entry.details) : null,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          timestamp: new Date()
        }
      })
    } catch (error) {
      console.error('Failed to create audit log:', error)
      // 不抛出错误，避免影响主要业务流程
    }
  }

  /**
   * 记录用户登录
   */
  static async logLogin(userId: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.log({
      userId,
      action: 'LOGIN',
      resource: 'AUTH',
      ipAddress,
      userAgent
    })
  }

  /**
   * 记录用户登出
   */
  static async logLogout(userId: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.log({
      userId,
      action: 'LOGOUT',
      resource: 'AUTH',
      ipAddress,
      userAgent
    })
  }

  /**
   * 记录客户操作
   */
  static async logCustomerAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    customerId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'CUSTOMER',
      resourceId: customerId,
      details,
      ipAddress
    })
  }

  /**
   * 记录项目档案操作
   */
  static async logArchiveAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    archiveId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'ARCHIVE',
      resourceId: archiveId,
      details,
      ipAddress
    })
  }

  /**
   * 记录服务工单操作
   */
  static async logServiceAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'STATUS_CHANGE',
    serviceId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'SERVICE',
      resourceId: serviceId,
      details,
      ipAddress
    })
  }

  /**
   * 记录配置操作
   */
  static async logConfigurationAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW',
    configId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'CONFIGURATION',
      resourceId: configId,
      details,
      ipAddress
    })
  }

  /**
   * 记录SLA操作
   */
  static async logSlaAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    slaId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'SLA',
      resourceId: slaId,
      details,
      ipAddress
    })
  }

  /**
   * 获取操作日志
   */
  static async getLogs(
    page: number = 1,
    limit: number = 50,
    userId?: string,
    resource?: string,
    action?: string,
    startDate?: Date,
    endDate?: Date
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (userId) where.userId = userId
    if (resource) where.resource = resource
    if (action) where.action = action
    
    if (startDate || endDate) {
      where.timestamp = {}
      if (startDate) where.timestamp.gte = startDate
      if (endDate) where.timestamp.lte = endDate
    }

    const [logs, total] = await Promise.all([
      prisma.operationLog.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        orderBy: { timestamp: 'desc' }
      }),
      prisma.operationLog.count({ where })
    ])

    return {
      logs: logs.map(log => ({
        ...log,
        details: log.details ? JSON.parse(log.details) : null
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取用户操作统计
   */
  static async getUserActionStats(
    userId?: string,
    startDate?: Date,
    endDate?: Date
  ) {
    const where: any = {}
    
    if (userId) where.userId = userId
    
    if (startDate || endDate) {
      where.timestamp = {}
      if (startDate) where.timestamp.gte = startDate
      if (endDate) where.timestamp.lte = endDate
    }

    const stats = await prisma.operationLog.groupBy({
      by: ['action', 'resource'],
      where,
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    return stats.map(stat => ({
      action: stat.action,
      resource: stat.resource,
      count: stat._count.id
    }))
  }
}