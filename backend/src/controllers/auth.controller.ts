import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'
import { hashPassword, verifyPassword } from '@/utils/crypto.util'
import { 
  generateTokenPair, 
  refreshAccessToken, 
  revokeRefreshToken,
  blacklistAccessToken,
  TokenPayload 
} from '@/utils/jwt.util'


// 验证Schema
const registerSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(6).max(100),
  fullName: z.string().optional(),
  phone: z.string().optional()
})

const loginSchema = z.object({
  username: z.string(),
  password: z.string()
})


// 用户注册
export const register = async (req: Request, res: Response) => {
  try {
    const { username, email, password, fullName, phone } = registerSchema.parse(req.body)

    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email }
        ]
      }
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      })
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        fullName: fullName || null,
        phone: phone || null
      },
      select: {
        id: true,
        username: true,
        email: true,
        fullName: true,
        phone: true,
        status: true,
        createdAt: true
      }
    })

    // 生成令牌对
    const tokenPayload: TokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email
    }

    const tokens = await generateTokenPair(tokenPayload)

    // 设置HttpOnly Cookie
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env['NODE_ENV'] === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
    })

    return res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user,
        accessToken: tokens.accessToken,
        expiresIn: tokens.expiresIn
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Register error:', error)
    return res.status(500).json({
      success: false,
      message: '注册失败'
    })
  }
}

// 用户登录
export const login = async (req: Request, res: Response) => {
  try {
    const { username, password } = loginSchema.parse(req.body)

    // 查找用户
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email: username }
        ]
      },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      })
    }

    // 检查用户状态
    if (user.status !== 'ACTIVE') {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用或停用'
      })
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      })
    }

    // 生成令牌对
    const tokenPayload: TokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email
    }

    const tokens = await generateTokenPair(tokenPayload)

    // 设置HttpOnly Cookie
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env['NODE_ENV'] === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
    })

    // 返回用户信息（排除密码）
    const { password: _, ...userWithoutPassword } = user
    const userRoles = user.userRoles.map(ur => ur.role)

    return res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          ...userWithoutPassword,
          roles: userRoles
        },
        accessToken: tokens.accessToken,
        expiresIn: tokens.expiresIn
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Login error:', error)
    return res.status(500).json({
      success: false,
      message: '登录失败'
    })
  }
}

// 刷新访问令牌
export const refresh = async (req: Request, res: Response) => {
  try {
    const refreshToken = req.cookies['refreshToken'] || req.body.refreshToken

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌缺失'
      })
    }

    // 刷新访问令牌
    const tokens = await refreshAccessToken(refreshToken)

    // 更新RefreshToken Cookie
    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env['NODE_ENV'] === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
    })

    return res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        accessToken: tokens.accessToken,
        expiresIn: tokens.expiresIn
      }
    })
  } catch (error) {
    console.error('Refresh error:', error)
    return res.status(401).json({
      success: false,
      message: '令牌刷新失败'
    })
  }
}

// 用户登出
export const logout = async (req: Request, res: Response) => {
  try {
    const refreshToken = req.cookies['refreshToken']
    const accessToken = req.headers.authorization?.replace('Bearer ', '')

    // 撤销刷新令牌
    if (refreshToken) {
      try {
        // 从刷新令牌中获取用户ID
        const { verifyRefreshToken } = await import('@/utils/jwt.util')
        const payload = verifyRefreshToken(refreshToken)
        await revokeRefreshToken(payload.userId)
      } catch (error) {
        console.error('Error revoking refresh token:', error)
      }
    }

    // 将访问令牌加入黑名单
    if (accessToken) {
      await blacklistAccessToken(accessToken)
    }

    // 清除Cookie
    res.clearCookie('refreshToken')

    return res.json({
      success: true,
      message: '登出成功'
    })
  } catch (error) {
    console.error('Logout error:', error)
    return res.status(500).json({
      success: false,
      message: '登出失败'
    })
  }
}

// 获取当前用户信息
export const me = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.userId

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      })
    }

    const userRoles = user.userRoles.map(ur => ur.role)

    const { password: _, ...userWithoutPassword } = user
    
    return res.json({
      success: true,
      data: {
        ...userWithoutPassword,
        roles: userRoles
      }
    })
  } catch (error) {
    console.error('Get me error:', error)
    return res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    })
  }
}

// 修改密码
export const changePassword = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.userId
    const { currentPassword, newPassword } = req.body

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '当前密码和新密码不能为空'
      })
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: '新密码长度不能少于6位'
      })
    }

    // 获取用户当前密码
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { password: true }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      })
    }

    // 验证当前密码
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password)
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '当前密码错误'
      })
    }

    // 哈希新密码
    const hashedNewPassword = await hashPassword(newPassword)

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword }
    })

    // 撤销所有刷新令牌，强制重新登录
    await revokeRefreshToken(userId)

    return res.json({
      success: true,
      message: '密码修改成功，请重新登录'
    })
  } catch (error) {
    console.error('Change password error:', error)
    return res.status(500).json({
      success: false,
      message: '密码修改失败'
    })
  }
}