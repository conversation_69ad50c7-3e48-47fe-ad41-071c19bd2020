import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import rateLimit from 'express-rate-limit'
import cookieParser from 'cookie-parser'
import dotenv from 'dotenv'

import { errorHandler } from '@/middleware/error.middleware'
import routes from '@/routes'
import { setupSwagger } from '@/config/swagger.config'
import { connectDatabase } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { SchedulerService } from '@/services/scheduler.service'

dotenv.config()

const app = express()
const PORT = process.env['PORT'] || 3001

// Security middleware
app.use(helmet())
app.use(cors({
  origin: process.env['FRONTEND_URL'] || 'http://localhost:3000',
  credentials: true
}))

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
})
app.use('/api', limiter)

// Middleware
app.use(morgan('combined'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(cookieParser())

// Health check
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// API Routes
app.use('/api', routes)

// Setup Swagger documentation
setupSwagger(app)

// Error handling middleware (must be last)
app.use(errorHandler)

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  })
})

async function startServer() {
  try {
    // 仅在生产环境连接数据库
    if (process.env['NODE_ENV'] === 'production') {
      await connectDatabase()
    } else {
      console.log('⚠️  Development mode: Skipping database connection')
    }
    
    // 初始化Redis连接
    console.log('🔗 Connecting to Redis...')
    // Redis连接会在第一次使用时自动建立
    
    // 初始化任务调度器
    SchedulerService.init()
    
    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`)
      console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`)
      console.log(`🏥 Health Check: http://localhost:${PORT}/health`)
      console.log('')
      console.log('📋 Available API Endpoints:')
      console.log('')
      console.log('  🔐 认证 (Authentication):')
      console.log('    POST /api/v1/auth/register - 用户注册')
      console.log('    POST /api/v1/auth/login - 用户登录')
      console.log('    POST /api/v1/auth/refresh - 刷新令牌')
      console.log('    POST /api/v1/auth/logout - 用户登出')
      console.log('    GET  /api/v1/auth/me - 获取当前用户信息')
      console.log('    POST /api/v1/auth/change-password - 修改密码')
      console.log('')
      console.log('  👥 客户管理 (Customers):')
      console.log('    GET  /api/v1/customers - 获取客户列表')
      console.log('    POST /api/v1/customers - 创建客户')
      console.log('    GET  /api/v1/customers/{id} - 获取客户详情')
      console.log('    PUT  /api/v1/customers/{id} - 更新客户')
      console.log('    DELETE /api/v1/customers/{id} - 删除客户')
      console.log('    GET  /api/v1/customers/stats - 客户统计')
      console.log('')
      console.log('  📁 项目档案 (Archives):')
      console.log('    GET  /api/v1/archives - 获取项目档案列表')
      console.log('    POST /api/v1/archives - 创建项目档案')
      console.log('    GET  /api/v1/archives/{id} - 获取项目档案详情')
      console.log('    PUT  /api/v1/archives/{id} - 更新项目档案')
      console.log('    DELETE /api/v1/archives/{id} - 删除项目档案')
      console.log('    GET  /api/v1/archives/stats - 项目档案统计')
      console.log('')
      console.log('  🎫 服务工单 (Services):')
      console.log('    GET  /api/v1/services - 获取服务工单列表')
      console.log('    POST /api/v1/services - 创建服务工单')
      console.log('    GET  /api/v1/services/{id} - 获取服务工单详情')
      console.log('    PUT  /api/v1/services/{id} - 更新服务工单')
      console.log('    DELETE /api/v1/services/{id} - 删除服务工单')
      console.log('    GET  /api/v1/services/stats - 服务工单统计')
      console.log('')
      console.log('  ⚙️  配置管理 (Configurations):')
      console.log('    GET  /api/v1/configurations - 获取配置列表')
      console.log('    POST /api/v1/configurations - 创建配置')
      console.log('    GET  /api/v1/configurations/{id} - 获取配置详情')
      console.log('    PUT  /api/v1/configurations/{id} - 更新配置')
      console.log('    DELETE /api/v1/configurations/{id} - 删除配置')
      console.log('    GET  /api/v1/configurations/stats - 配置统计')
    })
  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

startServer()

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('🛑 Shutting down server...')
  try {
    // 停止所有定时任务
    SchedulerService.stopAll()
    
    // 断开Redis连接
    await CacheService.disconnect()
    
    console.log('👋 Server shut down gracefully')
    process.exit(0)
  } catch (error) {
    console.error('❌ Error during shutdown:', error)
    process.exit(1)
  }
})