import Redis from 'ioredis'

const redisUrl = process.env['REDIS_URL'] || 'redis://localhost:6379'

export const redis = new Redis(redisUrl, {
  maxRetriesPerRequest: 3,
  lazyConnect: true
})

redis.on('connect', () => {
  console.log('✅ Redis connected successfully')
})

redis.on('error', (error) => {
  console.error('❌ Redis connection error:', error)
})

redis.on('close', () => {
  console.log('📦 Redis disconnected')
})

// Redis 缓存工具类
export class CacheService {
  // 设置缓存
  static async set(key: string, value: any, expireInSeconds = 3600): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value)
      await redis.setex(key, expireInSeconds, serializedValue)
    } catch (error) {
      console.error('Cache set error:', error)
      throw error
    }
  }

  // 获取缓存
  static async get<T>(key: string): Promise<T | null> {
    try {
      const value = await redis.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  // 删除缓存
  static async del(key: string): Promise<void> {
    try {
      await redis.del(key)
    } catch (error) {
      console.error('Cache delete error:', error)
      throw error
    }
  }

  // 删除匹配的缓存
  static async delPattern(pattern: string): Promise<void> {
    try {
      const keys = await redis.keys(pattern)
      if (keys.length > 0) {
        await redis.del(...keys)
      }
    } catch (error) {
      console.error('Cache delete pattern error:', error)
      throw error
    }
  }

  // 断开连接
  static async disconnect(): Promise<void> {
    try {
      await redis.disconnect()
    } catch (error) {
      console.error('Redis disconnect error:', error)
    }
  }

  // 检查缓存是否存在
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key)
      return result === 1
    } catch (error) {
      console.error('Cache exists error:', error)
      return false
    }
  }

  // 设置哈希缓存
  static async hset(key: string, field: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value)
      await redis.hset(key, field, serializedValue)
    } catch (error) {
      console.error('Cache hset error:', error)
      throw error
    }
  }

  // 获取哈希缓存
  static async hget<T>(key: string, field: string): Promise<T | null> {
    try {
      const value = await redis.hget(key, field)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Cache hget error:', error)
      return null
    }
  }

  // 获取所有哈希缓存
  static async hgetall<T>(key: string): Promise<Record<string, T>> {
    try {
      const hash = await redis.hgetall(key)
      const result: Record<string, T> = {}
      
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value)
      }
      
      return result
    } catch (error) {
      console.error('Cache hgetall error:', error)
      return {}
    }
  }
}