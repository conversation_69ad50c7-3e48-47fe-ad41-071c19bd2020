// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String
  fullName  String?  @map("full_name")
  phone     String?
  avatar    String?
  status    UserStatus @default(ACTIVE)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联
  userRoles        UserRole[]
  createdCustomers Customer[]         @relation("CustomerCreatedBy")
  createdArchives  ProjectArchive[]   @relation("ArchiveCreatedBy")
  assignedServices Service[]          @relation("ServiceAssignedTo")
  createdServices  Service[]          @relation("ServiceCreatedBy")
  workLogs         ServiceWorkLog[]
  uploads          ServiceAttachment[]
  comments         ServiceComment[]
  operationLogs    OperationLog[]

  @@map("users")
}

// 角色表
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  permissions Json     // 存储权限数组
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  userRoles UserRole[]

  @@map("roles")
}

// 用户角色关联表
model UserRole {
  userId String
  roleId String
  assignedAt DateTime @default(now()) @map("assigned_at")

  // 关联
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@id([userId, roleId])
  @@map("user_roles")
}

// 客户表
model Customer {
  id            String   @id @default(cuid())
  name          String
  company       String?
  industry      String?
  level         CustomerLevel @default(STANDARD)
  contactPerson String?  @map("contact_person")
  email         String?
  phone         String?
  address       String?
  description   String?  @db.Text
  createdBy     String   @map("created_by")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联
  createdByUser User              @relation("CustomerCreatedBy", fields: [createdBy], references: [id])
  contacts      CustomerContact[]
  archives      ProjectArchive[]

  @@map("customers")
}

// 客户联系人表
model CustomerContact {
  id         String   @id @default(cuid())
  customerId String   @map("customer_id")
  name       String
  position   String?
  email      String?
  phone      String?
  isPrimary  Boolean  @default(false) @map("is_primary")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 关联
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_contacts")
}

// 项目档案表 (简化后的项目信息，主要用于运维服务对象)
model ProjectArchive {
  id              String              @id @default(cuid())
  customerId      String              @map("customer_id")
  name            String
  description     String?             @db.Text
  technology      String?             // 技术栈
  environment     String?             // 部署环境
  version         String?             // 当前版本
  deploymentDate  DateTime?           @map("deployment_date") // 部署日期
  status          ArchiveStatus       @default(ACTIVE) // 档案状态
  createdBy       String              @map("created_by")
  createdAt       DateTime            @default(now()) @map("created_at")
  updatedAt       DateTime            @updatedAt @map("updated_at")

  // 关联
  customer        Customer              @relation(fields: [customerId], references: [id])
  createdByUser   User                  @relation("ArchiveCreatedBy", fields: [createdBy], references: [id])
  configurations  ProjectConfiguration[]
  services        Service[]

  @@map("project_archives")
}

// 项目配置表 (运维配置信息)
model ProjectConfiguration {
  id             String         @id @default(cuid())
  archiveId      String         @map("archive_id")
  configType     ConfigType     @map("config_type")
  title          String         // 配置项标题
  configData     Json           @map("config_data") // 存储配置数据
  encryptedFields Json?         @map("encrypted_fields") // 存储需要加密的字段名
  description    String?        // 配置说明
  isActive       Boolean        @default(true) @map("is_active")
  lastUpdated    DateTime?      @map("last_updated") // 最后更新时间
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // 关联
  archive ProjectArchive @relation(fields: [archiveId], references: [id], onDelete: Cascade)

  @@map("project_configurations")
}

// SLA模板表
model SlaTemplate {
  id             String   @id @default(cuid())
  name           String
  description    String?  @db.Text
  responseTime   Int      @map("response_time") // 响应时间(分钟)
  resolutionTime Int      @map("resolution_time") // 解决时间(小时)
  availability   Float    @default(99.9) // 可用性百分比
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // 关联
  services Service[]

  @@map("sla_templates")
}

// 运维服务记录表 (核心功能)
model Service {
  id                    String        @id @default(cuid())
  archiveId             String        @map("archive_id") // 关联项目档案
  slaTemplateId         String?       @map("sla_template_id")
  ticketNumber          String        @unique @map("ticket_number") // 工单号
  title                 String
  description           String        @db.Text
  category              ServiceCategory @default(MAINTENANCE) // 服务类别
  priority              Priority      @default(MEDIUM)
  status                ServiceStatus @default(PENDING)
  assignedTo            String?       @map("assigned_to")
  customerContact       String?       @map("customer_contact") // 客户联系人
  startTime             DateTime?     @map("start_time")
  endTime               DateTime?     @map("end_time")
  actualResponseTime    Int?          @map("actual_response_time") // 实际响应时间(分钟)
  actualResolutionTime  Int?          @map("actual_resolution_time") // 实际解决时间(小时)
  estimatedHours        Float?        @map("estimated_hours") // 预估工时
  actualHours           Float?        @map("actual_hours") // 实际工时
  resolution            String?       @db.Text // 解决方案
  customerFeedback      String?       @db.Text @map("customer_feedback") // 客户反馈
  satisfaction          Int?          // 客户满意度(1-5)
  tags                  Json?         // 标签
  createdBy             String        @map("created_by")
  createdAt             DateTime      @default(now()) @map("created_at")
  updatedAt             DateTime      @updatedAt @map("updated_at")

  // 关联
  archive       ProjectArchive @relation(fields: [archiveId], references: [id])
  slaTemplate   SlaTemplate?   @relation(fields: [slaTemplateId], references: [id])
  assignedUser  User?          @relation("ServiceAssignedTo", fields: [assignedTo], references: [id])
  createdByUser User           @relation("ServiceCreatedBy", fields: [createdBy], references: [id])
  workLogs      ServiceWorkLog[]
  attachments   ServiceAttachment[]
  comments      ServiceComment[]

  @@map("services")
}

// 服务工作日志表
model ServiceWorkLog {
  id          String   @id @default(cuid())
  serviceId   String   @map("service_id")
  userId      String   @map("user_id")
  description String   @db.Text
  workHours   Float    @map("work_hours") // 工作时长(小时)
  workDate    DateTime @map("work_date")
  category    WorkCategory @default(MAINTENANCE)
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id])

  @@map("service_work_logs")
}

// 服务附件表
model ServiceAttachment {
  id           String   @id @default(cuid())
  serviceId    String   @map("service_id")
  filename     String
  originalName String   @map("original_name")
  filePath     String   @map("file_path")
  fileSize     Int      @map("file_size")
  mimeType     String   @map("mime_type")
  uploadedBy   String   @map("uploaded_by")
  uploadedAt   DateTime @default(now()) @map("uploaded_at")

  // 关联
  service  Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  uploader User    @relation(fields: [uploadedBy], references: [id])

  @@map("service_attachments")
}

// 服务评论表
model ServiceComment {
  id         String   @id @default(cuid())
  serviceId  String   @map("service_id")
  content    String   @db.Text
  isInternal Boolean  @default(false) @map("is_internal") // 是否内部评论
  authorId   String   @map("author_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 关联
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  author  User    @relation(fields: [authorId], references: [id])

  @@map("service_comments")
}

// 通知模板表
model NotificationTemplate {
  id        String           @id @default(cuid())
  name      String
  type      NotificationType
  subject   String
  content   String           @db.Text
  variables Json?            // 可用变量
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @updatedAt @map("updated_at")

  // 关联
  notifications Notification[]

  @@map("notification_templates")
}

// 通知记录表
model Notification {
  id         String           @id @default(cuid())
  templateId String?          @map("template_id")
  recipient  String           // 接收者邮箱或手机号
  type       NotificationType
  subject    String
  content    String           @db.Text
  status     NotificationStatus @default(PENDING)
  sentAt     DateTime?        @map("sent_at")
  createdAt  DateTime         @default(now()) @map("created_at")

  // 关联
  template NotificationTemplate? @relation(fields: [templateId], references: [id])

  @@map("notifications")
}

// 操作日志表
model OperationLog {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  action    String   // 操作类型
  resource  String   // 操作资源
  resourceId String? @map("resource_id")
  details   Json?    // 操作详情
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联
  user User @relation(fields: [userId], references: [id])

  @@map("operation_logs")
}

// 枚举定义
enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}

enum CustomerLevel {
  BASIC
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum ArchiveStatus {
  ACTIVE
  MAINTENANCE
  DEPRECATED
  ARCHIVED
}

enum ServiceCategory {
  MAINTENANCE
  SUPPORT
  UPGRADE
  BUGFIX
  CONSULTING
  MONITORING
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ConfigType {
  SERVER
  DATABASE
  VPN
  ACCOUNT
  ENVIRONMENT
  OTHER
}

enum ServiceStatus {
  PENDING
  IN_PROGRESS
  WAITING_CUSTOMER
  RESOLVED
  CLOSED
}

enum NotificationType {
  EMAIL
  SMS
  SYSTEM
}

enum NotificationStatus {
  PENDING
  SENT
  FAILED
  CANCELLED
}

enum WorkCategory {
  ANALYSIS
  IMPLEMENTATION
  TESTING
  DOCUMENTATION
  COMMUNICATION
  MAINTENANCE
  SUPPORT
  OTHER
}