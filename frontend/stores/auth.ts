import { defineStore } from 'pinia'
import { jwtDecode } from 'jwt-decode'

export interface User {
  id: string
  username: string
  email: string
  name: string
  role: string
  permissions: string[]
  avatar?: string
  phone?: string
  department?: string
  position?: string
  status: 'active' | 'inactive'
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

export interface JWTPayload {
  userId: string
  username: string
  role: string
  permissions: string[]
  iat: number
  exp: number
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)

  // 计算属性
  const hasPermission = computed(() => (permission: string) => {
    if (!user.value) return false
    return user.value.permissions.includes(permission) || user.value.role === 'admin'
  })

  const hasRole = computed(() => (role: string) => {
    if (!user.value) return false
    return user.value.role === role
  })

  const isAdmin = computed(() => user.value?.role === 'admin')

  // 初始化认证状态
  const initAuth = () => {
    const savedToken = useCookie('auth-token', {
      default: () => null,
      secure: true,
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 7 // 7天
    })

    if (savedToken.value) {
      try {
        const decoded = jwtDecode<JWTPayload>(savedToken.value)
        
        // 检查token是否过期
        if (decoded.exp * 1000 > Date.now()) {
          token.value = savedToken.value
          isAuthenticated.value = true
          
          // 从token中恢复基本用户信息
          user.value = {
            id: decoded.userId,
            username: decoded.username,
            role: decoded.role,
            permissions: decoded.permissions,
            email: '',
            name: decoded.username,
            status: 'active',
            createdAt: '',
            updatedAt: ''
          }
          
          // 获取完整用户信息
          fetchUserProfile()
        } else {
          // token过期，清除认证状态
          clearAuth()
        }
      } catch (error) {
        console.error('Token decode error:', error)
        clearAuth()
      }
    }
  }

  // 登录
  const login = async (credentials: LoginCredentials) => {
    loading.value = true
    
    try {
      const { data } = await $fetch<{
        success: boolean
        data: {
          user: User
          token: string
        }
        message: string
      }>('/auth/login', {
        method: 'POST',
        body: credentials,
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (data.success) {
        // 保存认证信息
        token.value = data.data.token
        user.value = data.data.user
        isAuthenticated.value = true

        // 保存token到cookie
        const tokenCookie = useCookie('auth-token', {
          secure: true,
          sameSite: 'strict',
          maxAge: credentials.remember ? 60 * 60 * 24 * 30 : 60 * 60 * 24 * 7 // 记住我30天，否则7天
        })
        tokenCookie.value = data.data.token

        return { success: true, message: '登录成功' }
      } else {
        return { success: false, message: data.message || '登录失败' }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      return { 
        success: false, 
        message: error.data?.message || '登录失败，请检查网络连接' 
      }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      await $fetch('/auth/logout', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearAuth()
      await navigateTo('/login')
    }
  }

  // 清除认证状态
  const clearAuth = () => {
    user.value = null
    token.value = null
    isAuthenticated.value = false
    
    // 清除cookie
    const tokenCookie = useCookie('auth-token')
    tokenCookie.value = null
  }

  // 获取用户资料
  const fetchUserProfile = async () => {
    if (!token.value) return

    try {
      const { data } = await $fetch<{
        success: boolean
        data: User
      }>('/auth/profile', {
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (data.success) {
        user.value = data.data
      }
    } catch (error) {
      console.error('Fetch user profile error:', error)
      // 如果获取用户信息失败，可能是token无效
      clearAuth()
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: Partial<User>) => {
    if (!token.value) return { success: false, message: '未登录' }

    loading.value = true
    
    try {
      const { data } = await $fetch<{
        success: boolean
        data: User
        message: string
      }>('/auth/profile', {
        method: 'PUT',
        body: profileData,
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (data.success) {
        user.value = data.data
        return { success: true, message: data.message || '更新成功' }
      } else {
        return { success: false, message: data.message || '更新失败' }
      }
    } catch (error: any) {
      console.error('Update profile error:', error)
      return { 
        success: false, 
        message: error.data?.message || '更新失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }) => {
    if (!token.value) return { success: false, message: '未登录' }

    loading.value = true
    
    try {
      const { data } = await $fetch<{
        success: boolean
        message: string
      }>('/auth/change-password', {
        method: 'POST',
        body: passwordData,
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      return { success: data.success, message: data.message }
    } catch (error: any) {
      console.error('Change password error:', error)
      return { 
        success: false, 
        message: error.data?.message || '密码修改失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新token
  const refreshToken = async () => {
    if (!token.value) return false

    try {
      const { data } = await $fetch<{
        success: boolean
        data: { token: string }
      }>('/auth/refresh', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (data.success) {
        token.value = data.data.token
        
        // 更新cookie
        const tokenCookie = useCookie('auth-token')
        tokenCookie.value = data.data.token
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('Refresh token error:', error)
      clearAuth()
      return false
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isAuthenticated: readonly(isAuthenticated),
    loading: readonly(loading),
    
    // 计算属性
    hasPermission,
    hasRole,
    isAdmin,
    
    // 方法
    initAuth,
    login,
    logout,
    clearAuth,
    fetchUserProfile,
    updateProfile,
    changePassword,
    refreshToken
  }
})
