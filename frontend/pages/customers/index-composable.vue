<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader
      title="客户管理"
      description="管理系统中的所有客户信息"
      icon="i-heroicons-users"
      :actions="headerActions"
      @action="handleHeaderAction"
    />

    <!-- 数据表格 -->
    <DataTable
      :data="items"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :filters="filterConfig"
      :badge-config="badgeConfig"
      :action-items="getActionItems"
      search-placeholder="搜索客户名称、联系人或邮箱..."
      @search="handleSearch"
      @filter="handleFilter"
      @page-change="handlePageChange"
      @action="handleTableAction"
    >
      <!-- 自定义客户名称列 -->
      <template #name-data="{ row }">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-primary-700">
                {{ row.name.charAt(0) }}
              </span>
            </div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900">{{ row.name }}</div>
            <div class="text-sm text-gray-500">{{ row.code }}</div>
          </div>
        </div>
      </template>

      <!-- 自定义联系方式列 -->
      <template #contact-data="{ row }">
        <div>
          <div class="text-sm text-gray-900">{{ row.contactPerson }}</div>
          <div class="text-sm text-gray-500">{{ row.contactPhone }}</div>
          <div class="text-sm text-gray-500">{{ row.contactEmail }}</div>
        </div>
      </template>
    </DataTable>

    <!-- 创建客户模态框 -->
    <FormModal
      v-model="showCreateModal"
      title="新增客户"
      :fields="customerFields"
      :schema="customerSchema"
      :form-state="createForm.formState"
      :loading="creating"
      :field-options="fieldOptions"
      @submit="handleCreate"
      @cancel="handleCreateCancel"
    />

    <!-- 编辑客户模态框 -->
    <FormModal
      v-model="showEditModal"
      title="编辑客户"
      :fields="customerFields"
      :schema="customerSchema"
      :form-state="editForm.formState"
      :loading="updating"
      :field-options="fieldOptions"
      @submit="handleUpdate"
      @cancel="handleEditCancel"
    />

    <!-- 删除确认模态框 -->
    <ConfirmModal
      v-model="showDeleteModal"
      title="确认删除"
      :message="`确定要删除客户 ${selectedItem?.name} 吗？`"
      warning-message="此操作不可撤销，相关的项目和服务也将受到影响。"
      :loading="deleting"
      @confirm="handleDelete"
      @cancel="handleDeleteCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

const authStore = useAuthStore()

// 设置页面标题
useHead({
  title: '客户管理'
})

// 客户验证Schema
const customerSchema = z.object({
  name: z.string().min(1, '请输入客户名称'),
  code: z.string().min(1, '请输入客户编码'),
  type: z.enum(['enterprise', 'government', 'individual', 'other']),
  contactPerson: z.string().min(1, '请输入联系人'),
  contactPhone: z.string().min(1, '请输入联系电话'),
  contactEmail: z.string().email('请输入有效的邮箱地址'),
  address: z.string().optional(),
  description: z.string().optional()
})

// 使用CRUD组合函数
const {
  items,
  pagination,
  loading,
  creating,
  updating,
  deleting,
  selectedItem,
  loadItems,
  createItem,
  updateItem,
  deleteItem,
  setSelectedItem
} = useCrud({
  endpoint: '/customers',
  defaultPageSize: 10,
  onSuccess: (action, data) => {
    if (action === 'create') {
      showCreateModal.value = false
      createForm.resetForm()
    } else if (action === 'update') {
      showEditModal.value = false
      editForm.resetForm()
    } else if (action === 'delete') {
      showDeleteModal.value = false
      setSelectedItem(null)
    }
  }
})

// 使用搜索组合函数
const { handleSearch, handleFilter } = useSearch(loadItems)

// 使用表单组合函数
const createForm = useForm({
  name: '',
  code: '',
  type: 'enterprise',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  description: ''
})

const editForm = useForm({
  name: '',
  code: '',
  type: 'enterprise',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  description: ''
})

// 界面状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)

// 配置数据
const headerActions = computed(() => [
  {
    key: 'create',
    label: '新增客户',
    icon: 'i-heroicons-plus',
    show: authStore.hasPermission('customer:create')
  }
])

const columns = [
  { key: 'name', label: '客户名称', type: 'text' },
  { key: 'contact', label: '联系信息', type: 'text' },
  { key: 'type', label: '客户类型', type: 'badge' },
  { key: 'status', label: '状态', type: 'badge' },
  { key: 'createdAt', label: '创建时间', type: 'date' },
  { key: 'actions', label: '操作', type: 'actions' }
]

const filterConfig = [
  {
    key: 'status',
    placeholder: '状态筛选',
    options: [
      { label: '全部状态', value: '' },
      { label: '活跃', value: 'active' },
      { label: '停用', value: 'inactive' }
    ]
  },
  {
    key: 'type',
    placeholder: '类型筛选',
    options: [
      { label: '全部类型', value: '' },
      { label: '企业客户', value: 'enterprise' },
      { label: '政府机构', value: 'government' },
      { label: '个人客户', value: 'individual' },
      { label: '其他', value: 'other' }
    ]
  }
]

const badgeConfig = {
  status: {
    active: { color: 'green', text: '活跃' },
    inactive: { color: 'gray', text: '停用' }
  },
  type: {
    enterprise: { color: 'blue', text: '企业客户' },
    government: { color: 'purple', text: '政府机构' },
    individual: { color: 'green', text: '个人客户' },
    other: { color: 'gray', text: '其他' }
  }
}

const customerFields = [
  {
    type: 'grid',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 gap-4',
    fields: [
      { name: 'name', label: '客户名称', type: 'text', placeholder: '请输入客户名称', required: true },
      { name: 'code', label: '客户编码', type: 'text', placeholder: '请输入客户编码', required: true },
      { name: 'type', label: '客户类型', type: 'select', required: true },
      { name: 'contactPerson', label: '联系人', type: 'text', placeholder: '请输入联系人', required: true },
      { name: 'contactPhone', label: '联系电话', type: 'text', placeholder: '请输入联系电话', required: true },
      { name: 'contactEmail', label: '联系邮箱', type: 'email', placeholder: '请输入联系邮箱', required: true }
    ]
  },
  { name: 'address', label: '客户地址', type: 'textarea', placeholder: '请输入客户地址', rows: 2 },
  { name: 'description', label: '客户描述', type: 'textarea', placeholder: '请输入客户描述', rows: 3 }
]

const fieldOptions = {
  type: [
    { label: '企业客户', value: 'enterprise' },
    { label: '政府机构', value: 'government' },
    { label: '个人客户', value: 'individual' },
    { label: '其他', value: 'other' }
  ]
}

// 获取操作项
const getActionItems = (row: any) => {
  const items = []
  
  if (authStore.hasPermission('customer:read')) {
    items.push({
      action: 'view',
      label: '查看详情',
      icon: 'i-heroicons-eye'
    })
  }
  
  if (authStore.hasPermission('customer:update')) {
    items.push({
      action: 'edit',
      label: '编辑',
      icon: 'i-heroicons-pencil'
    })
  }
  
  if (authStore.hasPermission('customer:delete')) {
    items.push({
      action: 'delete',
      label: '删除',
      icon: 'i-heroicons-trash',
      color: 'red'
    })
  }
  
  return items
}

// 事件处理
const handleHeaderAction = (action: string) => {
  if (action === 'create') {
    showCreateModal.value = true
  }
}

const handleTableAction = (action: string, row: any) => {
  setSelectedItem(row)
  
  switch (action) {
    case 'view':
      navigateTo(`/customers/${row.id}`)
      break
    case 'edit':
      editForm.setFormData(row)
      showEditModal.value = true
      break
    case 'delete':
      showDeleteModal.value = true
      break
  }
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadItems()
}

// CRUD操作
const handleCreate = async (data: any) => {
  await createItem(data)
}

const handleUpdate = async (data: any) => {
  await updateItem(selectedItem.value.id, data)
}

const handleDelete = async () => {
  await deleteItem(selectedItem.value.id)
}

// 取消操作
const handleCreateCancel = () => {
  showCreateModal.value = false
  createForm.resetForm()
}

const handleEditCancel = () => {
  showEditModal.value = false
  editForm.resetForm()
  setSelectedItem(null)
}

const handleDeleteCancel = () => {
  showDeleteModal.value = false
  setSelectedItem(null)
}

// 页面加载时获取数据
onMounted(() => {
  loadItems()
})
</script>
