<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader
      :title="service?.title || '工单详情'"
      :description="`#${service?.ticketNumber} • ${service?.customerName}`"
      icon="i-heroicons-ticket"
      :status="getStatusText(service?.status)"
      :status-color="getStatusColor(service?.status)"
      show-back
      :actions="headerActions"
      @action="handleHeaderAction"
    />

    <!-- 工单概览 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 工单详细信息 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 基本信息卡片 -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">工单信息</h3>
              <div class="flex items-center space-x-2">
                <UBadge
                  :color="getTypeColor(service?.type)"
                  variant="soft"
                >
                  {{ getTypeText(service?.type) }}
                </UBadge>
                <UBadge
                  :color="getPriorityColor(service?.priority)"
                  variant="soft"
                >
                  {{ getPriorityText(service?.priority) }}
                </UBadge>
                <UBadge
                  v-if="service?.urgent"
                  color="red"
                  variant="soft"
                >
                  紧急
                </UBadge>
              </div>
            </div>
          </template>

          <div class="space-y-6">
            <!-- 工单描述 -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-2">问题描述</h4>
              <div class="text-sm text-gray-600 whitespace-pre-wrap">
                {{ service?.description || '暂无描述' }}
              </div>
            </div>

            <!-- 基本信息网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <dt class="text-sm font-medium text-gray-500">工单编号</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ service?.ticketNumber }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">客户</dt>
                <dd class="mt-1">
                  <NuxtLink
                    :to="`/customers/${service?.customerId}`"
                    class="text-sm text-primary-600 hover:text-primary-700"
                  >
                    {{ service?.customerName }}
                  </NuxtLink>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">关联项目</dt>
                <dd class="mt-1">
                  <NuxtLink
                    v-if="service?.projectId"
                    :to="`/projects/${service.projectId}`"
                    class="text-sm text-primary-600 hover:text-primary-700"
                  >
                    {{ service.projectName }}
                  </NuxtLink>
                  <span v-else class="text-sm text-gray-500">无关联项目</span>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">分类</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ getCategoryText(service?.category) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">创建时间</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(service?.createdAt) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">期望解决时间</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ service?.expectedResolution ? formatDate(service.expectedResolution) : '未设置' }}
                </dd>
              </div>
            </div>

            <!-- 附件列表 -->
            <div v-if="service?.attachments?.length > 0">
              <h4 class="text-sm font-medium text-gray-900 mb-2">附件</h4>
              <div class="space-y-2">
                <div
                  v-for="attachment in service.attachments"
                  :key="attachment.id"
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex items-center space-x-3">
                    <UIcon name="i-heroicons-document" class="w-5 h-5 text-gray-400" />
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ attachment.name }}</div>
                      <div class="text-xs text-gray-500">{{ formatFileSize(attachment.size) }}</div>
                    </div>
                  </div>
                  <UButton
                    size="sm"
                    variant="ghost"
                    icon="i-heroicons-arrow-down-tray"
                    @click="downloadAttachment(attachment)"
                  >
                    下载
                  </UButton>
                </div>
              </div>
            </div>
          </div>
        </UCard>

        <!-- 工作日志 -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">工作日志</h3>
              <UButton
                v-if="authStore.hasPermission('service:update')"
                size="sm"
                icon="i-heroicons-plus"
                @click="showLogModal = true"
              >
                添加日志
              </UButton>
            </div>
          </template>

          <div v-if="workLogs.length > 0" class="space-y-4">
            <div
              v-for="log in workLogs"
              :key="log.id"
              class="flex space-x-4"
            >
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-gray-600">
                    {{ log.createdBy?.charAt(0) }}
                  </span>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <div class="text-sm font-medium text-gray-900">{{ log.createdBy }}</div>
                  <div class="text-xs text-gray-500">{{ formatDate(log.createdAt) }}</div>
                </div>
                <div class="mt-1 text-sm text-gray-600 whitespace-pre-wrap">{{ log.content }}</div>
                <div v-if="log.timeSpent" class="mt-2 text-xs text-gray-500">
                  工时：{{ log.timeSpent }} 小时
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <UIcon name="i-heroicons-chat-bubble-left-right" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-500">暂无工作日志</p>
            <UButton
              v-if="authStore.hasPermission('service:update')"
              class="mt-4"
              @click="showLogModal = true"
            >
              添加第一条日志
            </UButton>
          </div>
        </UCard>
      </div>

      <!-- 侧边栏信息 -->
      <div class="space-y-6">
        <!-- 状态和分配信息 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">状态信息</h3>
          </template>

          <div class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">当前状态</dt>
              <dd class="mt-1">
                <UBadge
                  :color="getStatusColor(service?.status)"
                  variant="soft"
                >
                  {{ getStatusText(service?.status) }}
                </UBadge>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">分配给</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ service?.assignedTo || '未分配' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">处理部门</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ service?.department || '-' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">最后更新</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ formatDate(service?.updatedAt) }}</dd>
            </div>
          </div>
        </UCard>

        <!-- SLA信息 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">SLA信息</h3>
          </template>

          <div class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">SLA状态</dt>
              <dd class="mt-1 flex items-center space-x-2">
                <div
                  :class="getSlaStatusClass(service?.slaStatus)"
                  class="w-2 h-2 rounded-full"
                ></div>
                <span class="text-sm text-gray-900">
                  {{ getSlaStatusText(service?.slaStatus) }}
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">响应截止时间</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ service?.responseDeadline ? formatDate(service.responseDeadline) : '-' }}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">解决截止时间</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ service?.resolutionDeadline ? formatDate(service.resolutionDeadline) : '-' }}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">剩余时间</dt>
              <dd class="mt-1 text-sm text-gray-900">
                {{ formatSlaTime(service?.resolutionDeadline) }}
              </dd>
            </div>
          </div>
        </UCard>

        <!-- 快速操作 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">快速操作</h3>
          </template>

          <div class="space-y-3">
            <UButton
              v-if="authStore.hasPermission('service:update')"
              block
              variant="soft"
              icon="i-heroicons-pencil"
              @click="showEditModal = true"
            >
              编辑工单
            </UButton>
            <UButton
              v-if="authStore.hasPermission('service:update') && service?.status !== 'closed'"
              block
              variant="soft"
              icon="i-heroicons-user-plus"
              @click="showAssignModal = true"
            >
              分配工单
            </UButton>
            <UButton
              v-if="authStore.hasPermission('service:update') && service?.status === 'processing'"
              block
              variant="soft"
              icon="i-heroicons-check"
              color="green"
              @click="handleStatusChange('completed')"
            >
              标记完成
            </UButton>
            <UButton
              v-if="authStore.hasPermission('service:update') && service?.status === 'completed'"
              block
              variant="soft"
              icon="i-heroicons-lock-closed"
              color="gray"
              @click="handleStatusChange('closed')"
            >
              关闭工单
            </UButton>
          </div>
        </UCard>

        <!-- 相关信息 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">相关信息</h3>
          </template>

          <div class="space-y-3">
            <div class="text-sm">
              <span class="text-gray-500">总工时：</span>
              <span class="font-medium text-gray-900">{{ totalWorkHours }} 小时</span>
            </div>
            <div class="text-sm">
              <span class="text-gray-500">日志条数：</span>
              <span class="font-medium text-gray-900">{{ workLogs.length }} 条</span>
            </div>
            <div class="text-sm">
              <span class="text-gray-500">附件数量：</span>
              <span class="font-medium text-gray-900">{{ service?.attachments?.length || 0 }} 个</span>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 编辑工单模态框 -->
    <FormModal
      v-model="showEditModal"
      title="编辑工单"
      :fields="serviceFields"
      :schema="serviceSchema"
      :form-state="editForm.formState"
      :loading="updating"
      :field-options="fieldOptions"
      :modal-config="{ width: 'sm:max-w-4xl' }"
      @submit="handleUpdate"
      @cancel="handleEditCancel"
    />

    <!-- 分配工单模态框 -->
    <UModal v-model="showAssignModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">分配工单</h3>
        </template>

        <UForm
          :schema="assignSchema"
          :state="assignForm"
          @submit="handleAssign"
          class="space-y-4"
        >
          <UFormGroup label="分配给" name="assignedTo" required>
            <USelect
              v-model="assignForm.assignedTo"
              :options="userOptions"
              placeholder="请选择处理人员"
              value-attribute="value"
              option-attribute="label"
            />
          </UFormGroup>

          <UFormGroup label="部门" name="department">
            <USelect
              v-model="assignForm.department"
              :options="departmentOptions"
              placeholder="请选择部门"
              value-attribute="value"
              option-attribute="label"
            />
          </UFormGroup>

          <UFormGroup label="备注" name="note">
            <UTextarea
              v-model="assignForm.note"
              placeholder="分配备注（可选）"
              :rows="3"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showAssignModal = false"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="assigning"
            >
              确认分配
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>

    <!-- 添加工作日志模态框 -->
    <UModal v-model="showLogModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">添加工作日志</h3>
        </template>

        <UForm
          :schema="logSchema"
          :state="logForm"
          @submit="handleAddLog"
          class="space-y-4"
        >
          <UFormGroup label="日志内容" name="content" required>
            <UTextarea
              v-model="logForm.content"
              placeholder="请输入工作日志内容"
              :rows="4"
            />
          </UFormGroup>

          <UFormGroup label="工时（小时）" name="timeSpent">
            <UInput
              v-model="logForm.timeSpent"
              type="number"
              placeholder="请输入工时"
              step="0.5"
              min="0"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showLogModal = false"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="addingLog"
            >
              添加日志
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { z } from 'zod'

// 定义类型接口
interface ServiceDetail {
  id: string
  title: string
  description: string
  customerId: string
  customer: { name: string }
  projectId: string
  project: { name: string }
  type: string
  priority: string
  status: string
  assignedTo: string
  assignedUser: { name: string }
  department: string
  expectedResolution: string
  actualResolution: string
  slaDeadline: string
  createdAt: string
  updatedAt: string
}

interface WorkLog {
  id: string
  content: string
  timeSpent: number
  createdBy: string
  createdUser: { name: string }
  createdAt: string
}

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

const route = useRoute()
const authStore = useAuthStore()
const toast = useToast()
const { $apiClient } = useNuxtApp()

// 设置页面标题
useHead({
  title: computed(() => service.value?.title ? `${service.value.title} - 工单详情` : '工单详情')
})

// 验证Schema
const serviceSchema = z.object({
  title: z.string().min(1, '请输入工单标题'),
  description: z.string().min(1, '请输入工单描述'),
  customerId: z.string().min(1, '请选择客户'),
  projectId: z.string().optional(),
  type: z.enum(['incident', 'request', 'change', 'problem']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  category: z.string().min(1, '请选择分类'),
  urgent: z.boolean().optional(),
  expectedResolution: z.string().optional()
})

const assignSchema = z.object({
  assignedTo: z.string().min(1, '请选择处理人员'),
  department: z.string().optional(),
  note: z.string().optional()
})

const logSchema = z.object({
  content: z.string().min(1, '请输入日志内容'),
  timeSpent: z.string().optional().transform(val => val ? Number(val) : undefined)
})

// 数据状态
const service = ref<ServiceDetail | null>(null)
const workLogs = ref<WorkLog[]>([])

// 界面状态
const loading = ref(false)
const updating = ref(false)
const assigning = ref(false)
const addingLog = ref(false)
const showEditModal = ref(false)
const showAssignModal = ref(false)
const showLogModal = ref(false)

// 表单状态
const editForm = useForm({
  title: '',
  description: '',
  customerId: '',
  projectId: '',
  type: 'request',
  priority: 'medium',
  category: '',
  urgent: false,
  expectedResolution: ''
})

const assignForm = reactive({
  assignedTo: '',
  department: '',
  note: ''
})

const logForm = reactive({
  content: '',
  timeSpent: ''
})

// 配置数据
const headerActions = computed(() => [
  {
    key: 'edit',
    label: '编辑',
    icon: 'i-heroicons-pencil',
    show: authStore.hasPermission('service:update')
  },
  {
    key: 'assign',
    label: '分配',
    icon: 'i-heroicons-user-plus',
    show: authStore.hasPermission('service:update') && service.value?.status !== 'closed'
  }
])

const serviceFields = [
  {
    type: 'grid',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 gap-4',
    fields: [
      { name: 'title', label: '工单标题', type: 'text', placeholder: '请输入工单标题', required: true },
      { name: 'customerId', label: '客户', type: 'select', required: true },
      { name: 'projectId', label: '关联项目', type: 'select' },
      { name: 'type', label: '工单类型', type: 'select', required: true },
      { name: 'priority', label: '优先级', type: 'select', required: true },
      { name: 'category', label: '分类', type: 'select', required: true }
    ]
  },
  { name: 'description', label: '工单描述', type: 'textarea', placeholder: '请详细描述问题或需求', rows: 4, required: true },
  { name: 'expectedResolution', label: '期望解决时间', type: 'date' },
  { name: 'urgent', label: '紧急工单', type: 'checkbox', placeholder: '标记为紧急工单' }
]

const fieldOptions = ref({
  customerId: [],
  projectId: [],
  type: [
    { label: '事件', value: 'incident' },
    { label: '请求', value: 'request' },
    { label: '变更', value: 'change' },
    { label: '问题', value: 'problem' }
  ],
  priority: [
    { label: '低', value: 'low' },
    { label: '中', value: 'medium' },
    { label: '高', value: 'high' },
    { label: '紧急', value: 'urgent' }
  ],
  category: [
    { label: '技术支持', value: 'technical' },
    { label: '系统维护', value: 'maintenance' },
    { label: '功能开发', value: 'development' },
    { label: '数据处理', value: 'data' },
    { label: '其他', value: 'other' }
  ]
})

const userOptions = ref([])
const departmentOptions = ref([
  { label: '技术部', value: 'tech' },
  { label: '运维部', value: 'ops' },
  { label: '开发部', value: 'dev' },
  { label: '测试部', value: 'qa' }
])

// 计算属性
const totalWorkHours = computed(() => {
  return workLogs.value.reduce((total, log) => total + (log.timeSpent || 0), 0)
})

// 工具函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'yellow',
    processing: 'blue',
    completed: 'green',
    closed: 'gray'
  }
  return colors[status] || 'gray'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    closed: '已关闭'
  }
  return texts[status] || status
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    incident: 'red',
    request: 'blue',
    change: 'purple',
    problem: 'orange'
  }
  return colors[type] || 'gray'
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    incident: '事件',
    request: '请求',
    change: '变更',
    problem: '问题'
  }
  return texts[type] || type
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    low: 'gray',
    medium: 'blue',
    high: 'orange',
    urgent: 'red'
  }
  return colors[priority] || 'gray'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const getCategoryText = (category: string) => {
  const texts: Record<string, string> = {
    technical: '技术支持',
    maintenance: '系统维护',
    development: '功能开发',
    data: '数据处理',
    other: '其他'
  }
  return texts[category] || category
}

const getSlaStatusClass = (status: string) => {
  const classes = {
    normal: 'bg-green-500',
    warning: 'bg-yellow-500',
    critical: 'bg-red-500'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-500'
}

const getSlaStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    warning: '警告',
    critical: '超时'
  }
  return texts[status as keyof typeof texts] || '未知'
}

const formatSlaTime = (deadline: string) => {
  if (!deadline) return '-'
  const now = new Date()
  const deadlineDate = new Date(deadline)
  const diff = deadlineDate.getTime() - now.getTime()
  
  if (diff < 0) {
    return '已超时'
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 24) {
    const days = Math.floor(hours / 24)
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 数据加载
const loadServiceDetail = async () => {
  loading.value = true
  
  try {
    const serviceId = route.params.id as string
    
    const [serviceResponse, logsResponse] = await Promise.all([
      $apiClient(`/services/${serviceId}`),
      $apiClient(`/services/${serviceId}/logs`)
    ])

    service.value = serviceResponse.data
    workLogs.value = logsResponse.data

    // 填充编辑表单
    editForm.setFormData({
      ...service.value,
      expectedResolution: service.value.expectedResolution 
        ? new Date(service.value.expectedResolution).toISOString().split('T')[0] 
        : ''
    })
  } catch (error: any) {
    console.error('Load service detail error:', error)
    
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: '工单不存在'
      })
    }
    
    toast.add({
      title: '加载失败',
      description: '获取工单详情失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// 加载选项数据
const loadOptions = async () => {
  try {
    const [customersResponse, projectsResponse, usersResponse] = await Promise.all([
      apiClient.get('/customers?limit=100'),
      apiClient.get('/archives?limit=100'),
      apiClient.get('/users?limit=100')
    ])
    
    fieldOptions.value.customerId = customersResponse.data.items.map((item: any) => ({
      label: item.name,
      value: item.id
    }))
    
    fieldOptions.value.projectId = [
      { label: '无关联项目', value: '' },
      ...projectsResponse.data.items.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    ]
    
    userOptions.value = usersResponse.data.items.map((item: any) => ({
      label: item.name,
      value: item.id
    }))
  } catch (error) {
    console.error('Load options error:', error)
  }
}

// 事件处理
const handleHeaderAction = (action: string) => {
  switch (action) {
    case 'edit':
      showEditModal.value = true
      break
    case 'assign':
      Object.assign(assignForm, {
        assignedTo: service.value?.assignedTo || '',
        department: service.value?.department || '',
        note: ''
      })
      showAssignModal.value = true
      break
  }
}

const handleUpdate = async (data: any) => {
  updating.value = true
  
  try {
    const serviceId = route.params.id as string
    const response = await apiClient.put(`/services/${serviceId}`, data)
    
    service.value = response.data
    showEditModal.value = false
    
    toast.add({
      title: '更新成功',
      description: '工单信息已更新',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Update service error:', error)
    toast.add({
      title: '更新失败',
      description: error.message || '更新失败，请稍后重试',
      color: 'red'
    })
  } finally {
    updating.value = false
  }
}

const handleAssign = async () => {
  assigning.value = true
  
  try {
    const serviceId = route.params.id as string
    await apiClient.put(`/services/${serviceId}/assign`, assignForm)
    
    showAssignModal.value = false
    Object.assign(assignForm, { assignedTo: '', department: '', note: '' })
    await loadServiceDetail()
    
    toast.add({
      title: '分配成功',
      description: '工单已分配',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Assign service error:', error)
    toast.add({
      title: '分配失败',
      description: error.message || '分配失败，请稍后重试',
      color: 'red'
    })
  } finally {
    assigning.value = false
  }
}

const handleAddLog = async () => {
  addingLog.value = true
  
  try {
    const serviceId = route.params.id as string
    await apiClient.post(`/services/${serviceId}/logs`, logForm)
    
    showLogModal.value = false
    Object.assign(logForm, { content: '', timeSpent: '' })
    await loadServiceDetail()
    
    toast.add({
      title: '添加成功',
      description: '工作日志已添加',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Add log error:', error)
    toast.add({
      title: '添加失败',
      description: error.message || '添加失败，请稍后重试',
      color: 'red'
    })
  } finally {
    addingLog.value = false
  }
}

const handleStatusChange = async (newStatus: string) => {
  try {
    const serviceId = route.params.id as string
    await apiClient.put(`/services/${serviceId}/status`, { status: newStatus })
    
    await loadServiceDetail()
    
    toast.add({
      title: '状态更新成功',
      description: `工单状态已更新为${getStatusText(newStatus)}`,
      color: 'green'
    })
  } catch (error: any) {
    console.error('Update status error:', error)
    toast.add({
      title: '状态更新失败',
      description: error.message || '状态更新失败，请稍后重试',
      color: 'red'
    })
  }
}

const downloadAttachment = (attachment: any) => {
  // 实现附件下载逻辑
  toast.add({
    title: '功能开发中',
    description: '附件下载功能正在开发中',
    color: 'blue'
  })
}

const handleEditCancel = () => {
  showEditModal.value = false
  editForm.resetForm()
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadServiceDetail(),
    loadOptions()
  ])
})
</script>
