import { z } from 'zod'

export interface FormOptions {
  validateOnChange?: boolean
  validateOnBlur?: boolean
  resetOnSubmit?: boolean
}

export interface FormField {
  name: string
  label: string
  type: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  rows?: number
  options?: Array<{ label: string; value: any }>
  validation?: z.ZodSchema
  [key: string]: any
}

export const useForm = <T extends Record<string, any>>(
  initialData: T,
  options: FormOptions = {}
) => {
  const {
    validateOnChange = false,
    validateOnBlur = true,
    resetOnSubmit = false
  } = options

  // 表单状态
  const formState = reactive<T>({ ...initialData })
  const originalData = ref<T>({ ...initialData })
  const errors = ref<Record<string, string>>({})
  const touched = ref<Record<string, boolean>>({})
  const isSubmitting = ref(false)
  const isValidating = ref(false)

  // 验证单个字段
  const validateField = async (fieldName: keyof T, schema?: z.ZodSchema) => {
    if (!schema) return true

    try {
      await schema.parseAsync(formState[fieldName])
      delete errors.value[fieldName as string]
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        errors.value[fieldName as string] = error.errors[0]?.message || '验证失败'
      }
      return false
    }
  }

  // 验证整个表单
  const validateForm = async (schema?: z.ZodSchema) => {
    if (!schema) return true

    isValidating.value = true
    errors.value = {}

    try {
      await schema.parseAsync(formState)
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach(err => {
          const fieldName = err.path.join('.')
          errors.value[fieldName] = err.message
        })
      }
      return false
    } finally {
      isValidating.value = false
    }
  }

  // 设置字段值
  const setFieldValue = (fieldName: keyof T, value: any) => {
    formState[fieldName] = value
    
    if (validateOnChange) {
      // 这里可以添加字段级验证逻辑
    }
  }

  // 设置字段错误
  const setFieldError = (fieldName: keyof T, error: string) => {
    errors.value[fieldName as string] = error
  }

  // 清除字段错误
  const clearFieldError = (fieldName: keyof T) => {
    delete errors.value[fieldName as string]
  }

  // 标记字段为已触摸
  const setFieldTouched = (fieldName: keyof T, isTouched = true) => {
    touched.value[fieldName as string] = isTouched
  }

  // 设置表单数据
  const setFormData = (data: Partial<T>) => {
    Object.assign(formState, data)
  }

  // 重置表单
  const resetForm = (newData?: Partial<T>) => {
    const resetData = newData || originalData.value
    Object.assign(formState, resetData)
    errors.value = {}
    touched.value = {}
    isSubmitting.value = false
  }

  // 重置到初始状态
  const resetToInitial = () => {
    resetForm(initialData)
  }

  // 检查表单是否有变化
  const isDirty = computed(() => {
    return JSON.stringify(formState) !== JSON.stringify(originalData.value)
  })

  // 检查表单是否有效
  const isValid = computed(() => {
    return Object.keys(errors.value).length === 0
  })

  // 检查字段是否有错误
  const hasFieldError = (fieldName: keyof T) => {
    return !!errors.value[fieldName as string]
  }

  // 获取字段错误
  const getFieldError = (fieldName: keyof T) => {
    return errors.value[fieldName as string]
  }

  // 检查字段是否已触摸
  const isFieldTouched = (fieldName: keyof T) => {
    return !!touched.value[fieldName as string]
  }

  // 获取字段值
  const getFieldValue = (fieldName: keyof T) => {
    return formState[fieldName]
  }

  // 处理字段变化
  const handleFieldChange = (fieldName: keyof T, value: any) => {
    setFieldValue(fieldName, value)
    setFieldTouched(fieldName, true)
  }

  // 处理字段失焦
  const handleFieldBlur = (fieldName: keyof T) => {
    setFieldTouched(fieldName, true)
    
    if (validateOnBlur) {
      // 这里可以添加字段级验证逻辑
    }
  }

  // 提交表单
  const handleSubmit = async (
    onSubmit: (data: T) => Promise<void> | void,
    schema?: z.ZodSchema
  ) => {
    isSubmitting.value = true

    try {
      // 验证表单
      if (schema) {
        const isFormValid = await validateForm(schema)
        if (!isFormValid) {
          return false
        }
      }

      // 执行提交逻辑
      await onSubmit(formState)

      // 重置表单（如果配置了）
      if (resetOnSubmit) {
        resetForm()
      }

      return true
    } catch (error) {
      console.error('Form submit error:', error)
      return false
    } finally {
      isSubmitting.value = false
    }
  }

  return {
    // 状态
    formState,
    errors: readonly(errors),
    touched: readonly(touched),
    isSubmitting: readonly(isSubmitting),
    isValidating: readonly(isValidating),
    isDirty,
    isValid,

    // 方法
    validateField,
    validateForm,
    setFieldValue,
    setFieldError,
    clearFieldError,
    setFieldTouched,
    setFormData,
    resetForm,
    resetToInitial,
    hasFieldError,
    getFieldError,
    isFieldTouched,
    getFieldValue,
    handleFieldChange,
    handleFieldBlur,
    handleSubmit
  }
}

// 动态表单组合函数
export const useDynamicForm = (
  fields: FormField[],
  initialData: Record<string, any> = {},
  options: FormOptions = {}
) => {
  // 根据字段配置生成初始数据
  const generateInitialData = () => {
    const data: Record<string, any> = { ...initialData }
    
    fields.forEach(field => {
      if (!(field.name in data)) {
        switch (field.type) {
          case 'checkbox':
            data[field.name] = false
            break
          case 'number':
            data[field.name] = 0
            break
          case 'select':
            data[field.name] = field.options?.[0]?.value || ''
            break
          default:
            data[field.name] = ''
        }
      }
    })
    
    return data
  }

  const form = useForm(generateInitialData(), options)

  // 根据字段配置生成验证Schema
  const generateSchema = () => {
    const schemaFields: Record<string, z.ZodSchema> = {}
    
    fields.forEach(field => {
      let fieldSchema: z.ZodSchema = z.any()
      
      switch (field.type) {
        case 'text':
        case 'email':
        case 'password':
        case 'textarea':
          fieldSchema = z.string()
          if (field.required) {
            fieldSchema = fieldSchema.min(1, `${field.label}不能为空`)
          }
          if (field.type === 'email') {
            fieldSchema = (fieldSchema as z.ZodString).email('请输入有效的邮箱地址')
          }
          break
          
        case 'number':
          fieldSchema = z.number()
          if (field.required) {
            fieldSchema = fieldSchema.min(0, `${field.label}不能为空`)
          }
          break
          
        case 'select':
          fieldSchema = z.string()
          if (field.required) {
            fieldSchema = fieldSchema.min(1, `请选择${field.label}`)
          }
          break
          
        case 'checkbox':
          fieldSchema = z.boolean()
          break
          
        case 'date':
          fieldSchema = z.string()
          if (field.required) {
            fieldSchema = fieldSchema.min(1, `请选择${field.label}`)
          }
          break
      }
      
      // 如果字段有自定义验证，使用自定义验证
      if (field.validation) {
        fieldSchema = field.validation
      }
      
      schemaFields[field.name] = fieldSchema
    })
    
    return z.object(schemaFields)
  }

  const schema = generateSchema()

  // 获取字段配置
  const getFieldConfig = (fieldName: string) => {
    return fields.find(field => field.name === fieldName)
  }

  // 获取字段选项
  const getFieldOptions = (fieldName: string) => {
    const field = getFieldConfig(fieldName)
    return field?.options || []
  }

  // 检查字段是否必填
  const isFieldRequired = (fieldName: string) => {
    const field = getFieldConfig(fieldName)
    return field?.required || false
  }

  // 检查字段是否禁用
  const isFieldDisabled = (fieldName: string) => {
    const field = getFieldConfig(fieldName)
    return field?.disabled || false
  }

  // 检查字段是否只读
  const isFieldReadonly = (fieldName: string) => {
    const field = getFieldConfig(fieldName)
    return field?.readonly || false
  }

  return {
    ...form,
    fields,
    schema,
    getFieldConfig,
    getFieldOptions,
    isFieldRequired,
    isFieldDisabled,
    isFieldReadonly
  }
}

// 表单数组组合函数
export const useFormArray = <T>(
  initialItems: T[] = [],
  itemTemplate: T
) => {
  const items = ref<T[]>([...initialItems])

  // 添加项目
  const addItem = (item?: Partial<T>) => {
    const newItem = item ? { ...itemTemplate, ...item } : { ...itemTemplate }
    items.value.push(newItem as T)
    return items.value.length - 1
  }

  // 删除项目
  const removeItem = (index: number) => {
    if (index >= 0 && index < items.value.length) {
      items.value.splice(index, 1)
    }
  }

  // 移动项目
  const moveItem = (fromIndex: number, toIndex: number) => {
    if (fromIndex >= 0 && fromIndex < items.value.length &&
        toIndex >= 0 && toIndex < items.value.length) {
      const item = items.value.splice(fromIndex, 1)[0]
      items.value.splice(toIndex, 0, item)
    }
  }

  // 更新项目
  const updateItem = (index: number, updates: Partial<T>) => {
    if (index >= 0 && index < items.value.length) {
      Object.assign(items.value[index], updates)
    }
  }

  // 重置数组
  const resetArray = (newItems: T[] = []) => {
    items.value = [...newItems]
  }

  // 清空数组
  const clearArray = () => {
    items.value = []
  }

  return {
    items,
    addItem,
    removeItem,
    moveItem,
    updateItem,
    resetArray,
    clearArray
  }
}
