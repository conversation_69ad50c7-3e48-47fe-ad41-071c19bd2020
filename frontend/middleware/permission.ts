export default defineNuxtRouteMiddleware((to) => {
  const authStore = useAuthStore()

  // 检查用户是否已认证
  if (!authStore.isAuthenticated) {
    return navigateTo({
      path: '/login',
      query: {
        redirect: to.fullPath
      }
    })
  }

  // 根据路由检查权限
  const requiredPermissions = getRequiredPermissions(to.path)
  
  if (requiredPermissions.length > 0) {
    const hasPermission = requiredPermissions.some(permission => 
      authStore.hasPermission(permission)
    )

    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法访问此页面'
      })
    }
  }
})

// 根据路由路径获取所需权限
function getRequiredPermissions(path: string): string[] {
  const permissionMap: Record<string, string[]> = {
    '/admin': ['admin:access'],
    '/admin/configurations': ['configuration:read'],
    '/admin/sla': ['sla:read'],
    '/admin/notifications': ['notification:read'],
    '/customers': ['customer:read'],
    '/projects': ['archive:read'],
    '/services': ['service:read']
  }

  // 检查精确匹配
  if (permissionMap[path]) {
    return permissionMap[path]
  }

  // 检查路径前缀匹配
  for (const [routePath, permissions] of Object.entries(permissionMap)) {
    if (path.startsWith(routePath + '/')) {
      return permissions
    }
  }

  return []
}
