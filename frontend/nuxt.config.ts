export default defineNuxtConfig({
  devtools: { enabled: true },
  
  modules: [
    '@nuxt/ui',
    '@nuxt/image',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxtjs/tailwindcss'
  ],

  css: ['~/assets/css/main.css'],

  typescript: {
    strict: true,
    typeCheck: true,
    tsConfig: {
      compilerOptions: {
        strict: true,
        noImplicitAny: false,
        skipLibCheck: true,
        allowSyntheticDefaultImports: true,
        esModuleInterop: true,
        moduleResolution: "bundler",
        resolveJsonModule: true,
        isolatedModules: true,
        verbatimModuleSyntax: false
      }
    }
  },

  runtimeConfig: {
    // Private keys (only available on server-side)
    apiSecret: '',
    jwtSecret: '',
    
    // Public keys (exposed to client-side)
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3001/api/v1',
      appName: '运维服务管理系统',
      appVersion: '1.0.0'
    }
  },

  ssr: true,

  nitro: {
    experimental: {
      wasm: true
    }
  },

  ui: {
    global: true,
    icons: ['heroicons', 'lucide']
  },

  image: {
    format: ['webp']
  },

  app: {
    head: {
      title: '运维服务管理系统',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: '专业的软件项目运维服务管理系统' }
      ]
    }
  }
})