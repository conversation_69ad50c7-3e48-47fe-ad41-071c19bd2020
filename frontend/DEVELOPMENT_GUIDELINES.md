# 前端开发准则与代码质量规范

## 问题分析总结

通过对项目代码的质量检查，发现了以下主要问题类别：

### 1. 🚨 导入和依赖问题 (高优先级)

#### 问题描述
- 缺少必要的Vue 3 Composition API导入
- 缺少Nuxt 3特定功能的导入
- 第三方库导入方式错误

#### 具体错误
```typescript
// ❌ 错误：未导入Vue 3 Composition API
const loading = ref(false)  // 找不到名称"ref"
const data = reactive({})   // 找不到名称"reactive"
const computed = computed() // 找不到名称"computed"

// ❌ 错误：未导入Nuxt 3功能
definePageMeta({})  // 找不到名称"definePageMeta"
useHead({})         // 找不到名称"useHead"
navigateTo('/')     // 找不到名称"navigateTo"

// ❌ 错误：第三方库导入方式错误
import { VChart } from 'vue-echarts'  // 模块没有导出的成员"VChart"
```

#### 解决方案
```typescript
// ✅ 正确：导入Vue 3 Composition API
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

// ✅ 正确：Nuxt 3功能自动导入，无需手动导入
// definePageMeta, useHead, navigateTo 等由Nuxt自动提供

// ✅ 正确：第三方库导入
import VChart from 'vue-echarts'  // 默认导入
```

### 2. 🔧 类型安全问题 (高优先级)

#### 问题描述
- 大量使用`any`类型，失去TypeScript类型检查优势
- 函数参数类型推断不准确
- 数组类型定义不当

#### 具体错误
```typescript
// ❌ 错误：过度使用any类型
const customers = ref<any[]>([])
const handleAction = (action: any, data: any) => {}

// ❌ 错误：参数类型推断问题
items.push({...})  // 类型"object"的参数不能赋给类型"never"的参数
```

#### 解决方案
```typescript
// ✅ 正确：定义具体的接口类型
interface Customer {
  id: string
  name: string
  code: string
  type: 'enterprise' | 'individual' | 'government' | 'nonprofit'
  status: 'active' | 'inactive'
  // ... 其他属性
}

const customers = ref<Customer[]>([])

// ✅ 正确：明确函数参数类型
const handleAction = (action: string, data: Customer) => {}

// ✅ 正确：数组类型定义
const items: ActionItem[] = []
items.push({
  action: 'view',
  label: '查看详情',
  icon: 'i-heroicons-eye'
})
```

### 3. 🏗️ 架构和组织问题 (中优先级)

#### 问题描述
- 组合函数使用不一致
- 缺少统一的API客户端
- 状态管理分散

#### 具体错误
```typescript
// ❌ 错误：直接使用未定义的apiClient
const response = await apiClient.get('/customers')  // 找不到名称"apiClient"

// ❌ 错误：重复的状态管理代码
const loading = ref(false)
const creating = ref(false)
const updating = ref(false)
// 每个页面都重复这些状态
```

#### 解决方案
```typescript
// ✅ 正确：使用统一的API客户端
// plugins/api.client.ts
export default defineNuxtPlugin(() => {
  const apiClient = $fetch.create({
    baseURL: '/api',
    // 配置选项
  })
  
  return {
    provide: {
      apiClient
    }
  }
})

// ✅ 正确：使用组合函数统一状态管理
const {
  items,
  loading,
  creating,
  updating,
  deleting,
  loadItems,
  createItem,
  updateItem,
  deleteItem
} = useCrud({
  endpoint: '/customers'
})
```

### 4. 🎯 代码规范问题 (中优先级)

#### 问题描述
- 未使用的变量和参数
- 不一致的命名规范
- 缺少错误处理

#### 具体错误
```typescript
// ❌ 错误：未使用的参数
const getActionItems = (row: any) => {  // 已声明"row"，但从未读取其值
  const items = []
  // ... 没有使用row参数
}

// ❌ 错误：不一致的命名
const handleCreate = async (data: any) => {}
const onSubmit = async (formData: any) => {}  // 应该统一使用handle前缀
```

#### 解决方案
```typescript
// ✅ 正确：移除未使用的参数或使用下划线前缀
const getActionItems = () => {  // 如果不需要参数就移除
  const items = []
  return items
}

// 或者使用下划线表示未使用
const getActionItems = (_row: Customer) => {
  const items = []
  return items
}

// ✅ 正确：统一命名规范
const handleCreate = async (data: Customer) => {}
const handleSubmit = async (formData: Customer) => {}
```

## 🎯 开发准则

### 1. 导入规范

```typescript
// 1.1 Vue 3 Composition API导入
import { 
  ref, 
  reactive, 
  computed, 
  watch, 
  onMounted, 
  onUnmounted,
  nextTick
} from 'vue'

// 1.2 第三方库导入检查
// 使用前先检查库的导出方式
import VChart from 'vue-echarts'  // 默认导入
import { z } from 'zod'            // 命名导入

// 1.3 Nuxt 3自动导入
// 以下功能由Nuxt自动提供，无需手动导入：
// - definePageMeta
// - useHead
// - navigateTo
// - useRoute
// - useRouter
// - useState
// - useCookie
```

### 2. 类型安全规范

```typescript
// 2.1 定义明确的接口
interface Customer {
  id: string
  name: string
  code: string
  type: CustomerType
  status: CustomerStatus
  createdAt: string
  updatedAt: string
}

type CustomerType = 'enterprise' | 'individual' | 'government' | 'nonprofit'
type CustomerStatus = 'active' | 'inactive'

// 2.2 避免使用any类型
// ❌ 避免
const data = ref<any>({})

// ✅ 推荐
const data = ref<Customer>({} as Customer)

// 2.3 函数参数类型明确
const handleAction = (action: string, item: Customer) => {
  // 实现逻辑
}
```

### 3. 组合函数使用规范

```typescript
// 3.1 统一使用CRUD组合函数
const {
  items,
  pagination,
  loading,
  creating,
  updating,
  deleting,
  selectedItem,
  loadItems,
  createItem,
  updateItem,
  deleteItem,
  setSelectedItem
} = useCrud<Customer>({
  endpoint: '/customers',
  defaultPageSize: 15,
  onSuccess: (action, data) => {
    // 成功回调
  }
})

// 3.2 统一使用搜索组合函数
const { handleSearch, handleFilter } = useSearch(loadItems)

// 3.3 统一使用表单组合函数
const createForm = useForm<CreateCustomerForm>({
  name: '',
  code: '',
  type: 'enterprise'
})
```

### 4. 错误处理规范

```typescript
// 4.1 统一错误处理
const handleCreate = async (data: Customer) => {
  try {
    await createItem(data)
    // 成功处理由组合函数内部完成
  } catch (error) {
    console.error('Create customer error:', error)
    // 错误提示由组合函数内部完成
  }
}

// 4.2 API错误处理
const loadData = async () => {
  try {
    const response = await $fetch('/api/customers')
    return response
  } catch (error: any) {
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: '资源不存在'
      })
    }
    throw error
  }
}
```

### 5. 代码组织规范

```typescript
// 5.1 页面组件结构
<script setup lang="ts">
// 1. 导入
import { z } from 'zod'

// 2. 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

// 3. 页面标题
useHead({
  title: '页面标题'
})

// 4. 验证Schema
const schema = z.object({
  // 定义
})

// 5. 组合函数
const { ... } = useCrud({})
const { ... } = useSearch()
const { ... } = useForm()

// 6. 响应式状态
const showModal = ref(false)

// 7. 计算属性
const computedValue = computed(() => {})

// 8. 方法
const handleAction = () => {}

// 9. 生命周期
onMounted(() => {})
</script>
```

### 6. 性能优化规范

```typescript
// 6.1 防抖搜索
const { handleSearch } = useSearch(loadItems, {
  debounceMs: 300
})

// 6.2 懒加载
const { data } = await useLazyFetch('/api/data')

// 6.3 缓存策略
const { data } = await useFetch('/api/data', {
  key: 'unique-key',
  server: true
})
```

## 🔍 代码检查清单

### 提交前检查
- [ ] 所有必要的导入都已添加
- [ ] 没有使用`any`类型（除非必要）
- [ ] 所有函数参数都有明确类型
- [ ] 没有未使用的变量和参数
- [ ] 错误处理完整
- [ ] 组合函数使用一致
- [ ] 命名规范统一

### 代码审查重点
- [ ] 类型安全性
- [ ] 组件复用性
- [ ] 性能优化
- [ ] 错误处理
- [ ] 用户体验

## 🛠️ 工具配置

### ESLint配置
```json
{
  "extends": [
    "@nuxt/eslint-config",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "vue/no-unused-components": "error"
  }
}
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitAny": true
  }
}
```

## 📚 学习资源

- [Vue 3 Composition API](https://vuejs.org/guide/extras/composition-api-faq.html)
- [Nuxt 3 Documentation](https://nuxt.com/)
- [TypeScript Best Practices](https://typescript-eslint.io/rules/)
- [Vue 3 TypeScript Guide](https://vuejs.org/guide/typescript/overview.html)

---

**记住：代码质量是团队协作的基础，遵循这些准则能够提高代码的可维护性、可读性和稳定性。**
