<template>
  <div class="flex items-center justify-between">
    <!-- 左侧：标题和描述 -->
    <div class="flex items-center space-x-4">
      <!-- 返回按钮 -->
      <UButton
        v-if="showBack"
        icon="i-heroicons-arrow-left"
        variant="ghost"
        @click="handleBack"
      >
        {{ backText }}
      </UButton>

      <!-- 标题区域 -->
      <div>
        <div class="flex items-center space-x-3">
          <!-- 图标 -->
          <div v-if="icon" :class="iconContainerClass">
            <UIcon :name="icon" :class="iconClass" />
          </div>

          <!-- 标题 -->
          <h1 :class="titleClass">
            {{ title }}
          </h1>

          <!-- 状态徽章 -->
          <UBadge
            v-if="status"
            :color="statusColor"
            variant="soft"
          >
            {{ status }}
          </UBadge>
        </div>

        <!-- 描述 -->
        <p v-if="description" :class="descriptionClass">
          {{ description }}
        </p>

        <!-- 面包屑 -->
        <nav v-if="breadcrumbs.length > 0" class="flex mt-2" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-2">
            <li v-for="(crumb, index) in breadcrumbs" :key="index" class="flex items-center">
              <UIcon
                v-if="index > 0"
                name="i-heroicons-chevron-right"
                class="w-4 h-4 text-gray-400 mx-2"
              />
              <NuxtLink
                v-if="crumb.to"
                :to="crumb.to"
                class="text-sm text-gray-500 hover:text-gray-700"
              >
                {{ crumb.label }}
              </NuxtLink>
              <span v-else class="text-sm text-gray-900">
                {{ crumb.label }}
              </span>
            </li>
          </ol>
        </nav>
      </div>
    </div>

    <!-- 右侧：操作按钮 -->
    <div class="flex items-center space-x-3">
      <!-- 统计信息 -->
      <div v-if="stats.length > 0" class="hidden sm:flex items-center space-x-6 mr-6">
        <div
          v-for="stat in stats"
          :key="stat.label"
          class="text-center"
        >
          <div class="text-lg font-semibold text-gray-900">{{ stat.value }}</div>
          <div class="text-xs text-gray-500">{{ stat.label }}</div>
        </div>
      </div>

      <!-- 自定义操作插槽 -->
      <slot name="actions" />

      <!-- 预定义操作按钮 -->
      <template v-for="action in actions" :key="action.key">
        <UButton
          v-if="action.show !== false"
          :icon="action.icon"
          :color="action.color"
          :variant="action.variant"
          :size="action.size"
          :loading="action.loading"
          :disabled="action.disabled"
          @click="handleAction(action.key)"
        >
          {{ action.label }}
        </UButton>
      </template>

      <!-- 更多操作下拉菜单 -->
      <UDropdown v-if="moreActions.length > 0" :items="moreActionItems">
        <UButton
          color="gray"
          variant="ghost"
          icon="i-heroicons-ellipsis-vertical"
        />
      </UDropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Breadcrumb {
  label: string
  to?: string
}

interface Action {
  key: string
  label: string
  icon?: string
  color?: string
  variant?: string
  size?: string
  loading?: boolean
  disabled?: boolean
  show?: boolean
}

interface MoreAction {
  key: string
  label: string
  icon?: string
  color?: string
  disabled?: boolean
  show?: boolean
}

interface Stat {
  label: string
  value: string | number
}

interface Props {
  title: string
  description?: string
  icon?: string
  iconColor?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray'
  status?: string
  statusColor?: string
  showBack?: boolean
  backText?: string
  breadcrumbs?: Breadcrumb[]
  actions?: Action[]
  moreActions?: MoreAction[]
  stats?: Stat[]
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  backText: '返回',
  breadcrumbs: () => [],
  actions: () => [],
  moreActions: () => [],
  stats: () => [],
  size: 'md',
  iconColor: 'blue',
  statusColor: 'blue'
})

const emit = defineEmits<{
  back: []
  action: [key: string]
}>()

const router = useRouter()

// 计算属性
const titleClass = computed(() => {
  const sizeClasses = {
    sm: 'text-lg font-bold text-gray-900',
    md: 'text-2xl font-bold text-gray-900',
    lg: 'text-3xl font-bold text-gray-900'
  }
  return sizeClasses[props.size]
})

const descriptionClass = computed(() => {
  const sizeClasses = {
    sm: 'mt-1 text-xs text-gray-500',
    md: 'mt-1 text-sm text-gray-500',
    lg: 'mt-2 text-base text-gray-500'
  }
  return sizeClasses[props.size]
})

const iconContainerClass = computed(() => {
  const colorClasses = {
    blue: 'w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center',
    green: 'w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center',
    yellow: 'w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center',
    red: 'w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center',
    purple: 'w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center',
    gray: 'w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center'
  }
  return colorClasses[props.iconColor]
})

const iconClass = computed(() => {
  const colorClasses = {
    blue: 'w-5 h-5 text-blue-600',
    green: 'w-5 h-5 text-green-600',
    yellow: 'w-5 h-5 text-yellow-600',
    red: 'w-5 h-5 text-red-600',
    purple: 'w-5 h-5 text-purple-600',
    gray: 'w-5 h-5 text-gray-600'
  }
  return colorClasses[props.iconColor]
})

const moreActionItems = computed(() => {
  return props.moreActions
    .filter(action => action.show !== false)
    .map(action => ({
      label: action.label,
      icon: action.icon,
      disabled: action.disabled,
      click: () => handleAction(action.key)
    }))
})

// 事件处理
const handleBack = () => {
  emit('back')
  if (!emit('back')) {
    router.back()
  }
}

const handleAction = (key: string) => {
  emit('action', key)
}
</script>
