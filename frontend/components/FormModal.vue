<template>
  <UModal v-model="isOpen" :ui="modalConfig">
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">
          {{ title }}
        </h3>
      </template>

      <UForm
        :schema="schema"
        :state="formState"
        @submit="handleSubmit"
        class="space-y-4"
      >
        <!-- 动态表单字段 -->
        <template v-for="field in fields" :key="field.name">
          <!-- 网格布局容器 -->
          <div
            v-if="field.type === 'grid'"
            :class="field.gridClass || 'grid grid-cols-1 md:grid-cols-2 gap-4'"
          >
            <template v-for="gridField in field.fields" :key="gridField.name">
              <FormField
                :field="gridField"
                :form-state="formState"
                :options="fieldOptions[gridField.name]"
              />
            </template>
          </div>
          
          <!-- 单个字段 -->
          <FormField
            v-else
            :field="field"
            :form-state="formState"
            :options="fieldOptions[field.name]"
          />
        </template>

        <!-- 自定义内容插槽 -->
        <slot name="form-content" :form-state="formState" />

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3">
          <UButton
            color="gray"
            variant="soft"
            @click="handleCancel"
            :disabled="loading"
          >
            {{ cancelText }}
          </UButton>
          <UButton
            type="submit"
            :loading="loading"
            :disabled="loading"
          >
            {{ submitText }}
          </UButton>
        </div>
      </UForm>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import type { ZodSchema } from 'zod'

interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox' | 'grid'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  rows?: number
  min?: number
  max?: number
  step?: number
  gridClass?: string
  fields?: FormField[] // 用于grid类型
}

interface Props {
  modelValue: boolean
  title: string
  fields: FormField[]
  schema: ZodSchema
  formState: Record<string, any>
  loading?: boolean
  submitText?: string
  cancelText?: string
  modalConfig?: Record<string, any>
  fieldOptions?: Record<string, Array<{ label: string; value: any }>>
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  submitText: '确定',
  cancelText: '取消',
  modalConfig: () => ({ width: 'sm:max-w-md' }),
  fieldOptions: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  submit: [data: any]
  cancel: []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleSubmit = (data: any) => {
  emit('submit', data)
}

const handleCancel = () => {
  emit('cancel')
  isOpen.value = false
}
</script>
