<template>
  <UFormGroup
    :label="field.label"
    :name="field.name"
    :required="field.required"
  >
    <!-- 文本输入 -->
    <UInput
      v-if="field.type === 'text'"
      v-model="formState[field.name]"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 邮箱输入 -->
    <UInput
      v-else-if="field.type === 'email'"
      v-model="formState[field.name]"
      type="email"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 密码输入 -->
    <UInput
      v-else-if="field.type === 'password'"
      v-model="formState[field.name]"
      type="password"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 数字输入 -->
    <UInput
      v-else-if="field.type === 'number'"
      v-model="formState[field.name]"
      type="number"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
      :min="field.min"
      :max="field.max"
      :step="field.step"
    />

    <!-- 日期输入 -->
    <UInput
      v-else-if="field.type === 'date'"
      v-model="formState[field.name]"
      type="date"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 选择框 -->
    <USelect
      v-else-if="field.type === 'select'"
      v-model="formState[field.name]"
      :options="options"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
      value-attribute="value"
      option-attribute="label"
    />

    <!-- 文本域 -->
    <UTextarea
      v-else-if="field.type === 'textarea'"
      v-model="formState[field.name]"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
      :rows="field.rows || 3"
    />

    <!-- 复选框 -->
    <UCheckbox
      v-else-if="field.type === 'checkbox'"
      v-model="formState[field.name]"
      :label="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 默认文本输入 -->
    <UInput
      v-else
      v-model="formState[field.name]"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />
  </UFormGroup>
</template>

<script setup lang="ts">
interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  rows?: number
  min?: number
  max?: number
  step?: number
}

interface Props {
  field: FormField
  formState: Record<string, any>
  options?: Array<{ label: string; value: any }>
}

defineProps<Props>()
</script>
